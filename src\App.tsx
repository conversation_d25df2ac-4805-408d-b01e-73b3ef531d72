import "./App.css";
import { Grommet, FileInput } from "grommet";
const theme = {
  global: {
    font: {
      family: "Roboto",
      size: "18px",
      height: "20px",
    },
  },
};

const fileChange = (
  event: React.ChangeEvent<HTMLInputElement>,
  files: FileList
) => {
  console.log(event.target.files);
};

function App() {
  return (
    <Grommet full theme={theme}>
      <FileInput onChange={fileChange}></FileInput>
    </Grommet>
  );
}

export default App;
